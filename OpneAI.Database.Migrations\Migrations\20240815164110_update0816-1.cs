﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OpneAI.Database.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class update08161 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Em<PERSON>",
                table: "User",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "Account",
                table: "User",
                newName: "Email");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "TestPaper",
                newName: "Title");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Name",
                table: "User",
                newName: "Emai");

            migrationBuilder.RenameColumn(
                name: "Email",
                table: "User",
                newName: "Account");

            migrationBuilder.RenameColumn(
                name: "Title",
                table: "TestPaper",
                newName: "Name");
        }
    }
}
