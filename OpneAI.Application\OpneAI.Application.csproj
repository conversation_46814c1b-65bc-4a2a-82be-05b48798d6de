﻿<Project Sdk="Microsoft.NET.Sdk">




	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<NoWarn>1701;1702;1591</NoWarn>
		<DocumentationFile>OpneAI.Application.xml</DocumentationFile>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="applicationsettings.json" />
		<None Remove="OpneAI.Application.xml" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="applicationsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\OpneAI.Core\OpneAI.Core.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="System\Services\" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Include="AlibabaCloud.OpenApiClient" Version="0.1.12" />
	  <PackageReference Include="AlibabaCloud.SDK.Bailian20231229" Version="1.10.1" />
	  <PackageReference Include="AlibabaCloud.TeaConsole" Version="0.1.0" />
	  <PackageReference Include="AlibabaCloud.TeaUtil" Version="0.1.19" />
	  <PackageReference Include="AngleSharp" Version="1.3.0" />
	  <PackageReference Include="AWSSDK.S3" Version="4.0.6.2" />
	  <PackageReference Include="Google_GenerativeAI" Version="2.7.0" />
	  <PackageReference Include="OpenAI" Version="2.2.0" />
	  <PackageReference Include="pythonnet" Version="3.0.4" />
	  <PackageReference Include="Tea" Version="1.1.3" />
	  <PackageReference Include="TencentCloudSDK" Version="3.0.1067" />
	</ItemGroup>

</Project>
