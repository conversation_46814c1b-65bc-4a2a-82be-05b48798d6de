﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34511.84
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpneAI.Application", "OpneAI.Application\OpneAI.Application.csproj", "{FD7581A5-237E-4DC9-B478-ABC239BE33AB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpneAI.Core", "OpneAI.Core\OpneAI.Core.csproj", "{C77655A4-F147-4478-B0F6-839D343DD587}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpneAI.Database.Migrations", "OpneAI.Database.Migrations\OpneAI.Database.Migrations.csproj", "{5A5C7D5D-D0DE-4954-9761-F5E1ABC679A8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpneAI.EntityFramework.Core", "OpneAI.EntityFramework.Core\OpneAI.EntityFramework.Core.csproj", "{B146293E-A4AD-4B95-98E0-2A35C2C48FE5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpneAI.Web.Core", "OpneAI.Web.Core\OpneAI.Web.Core.csproj", "{1457B3AD-2F8F-43A5-BCC2-09EF043D245B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpneAI.Web.Entry", "OpneAI.Web.Entry\OpneAI.Web.Entry.csproj", "{8649DC2A-9C01-4E01-B4E2-C83997581BCD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FD7581A5-237E-4DC9-B478-ABC239BE33AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD7581A5-237E-4DC9-B478-ABC239BE33AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD7581A5-237E-4DC9-B478-ABC239BE33AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD7581A5-237E-4DC9-B478-ABC239BE33AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{C77655A4-F147-4478-B0F6-839D343DD587}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C77655A4-F147-4478-B0F6-839D343DD587}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C77655A4-F147-4478-B0F6-839D343DD587}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C77655A4-F147-4478-B0F6-839D343DD587}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A5C7D5D-D0DE-4954-9761-F5E1ABC679A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A5C7D5D-D0DE-4954-9761-F5E1ABC679A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A5C7D5D-D0DE-4954-9761-F5E1ABC679A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A5C7D5D-D0DE-4954-9761-F5E1ABC679A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{B146293E-A4AD-4B95-98E0-2A35C2C48FE5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B146293E-A4AD-4B95-98E0-2A35C2C48FE5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B146293E-A4AD-4B95-98E0-2A35C2C48FE5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B146293E-A4AD-4B95-98E0-2A35C2C48FE5}.Release|Any CPU.Build.0 = Release|Any CPU
		{1457B3AD-2F8F-43A5-BCC2-09EF043D245B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1457B3AD-2F8F-43A5-BCC2-09EF043D245B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1457B3AD-2F8F-43A5-BCC2-09EF043D245B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1457B3AD-2F8F-43A5-BCC2-09EF043D245B}.Release|Any CPU.Build.0 = Release|Any CPU
		{8649DC2A-9C01-4E01-B4E2-C83997581BCD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8649DC2A-9C01-4E01-B4E2-C83997581BCD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8649DC2A-9C01-4E01-B4E2-C83997581BCD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8649DC2A-9C01-4E01-B4E2-C83997581BCD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F8D05B9B-B0AB-448D-921B-74B33DCAF4F6}
	EndGlobalSection
EndGlobal
