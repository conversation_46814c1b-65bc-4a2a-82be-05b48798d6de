﻿using AlibabaCloud.SDK.Bailian20231229.Models;
using OpneAI.Application.System.Dtos;
using OpneAI.Core.Entities;

namespace OpneAI.Application
{
    public interface ISystemService
    {
        Task<List<AnswerDto>> AnswerQuestion(TestPaperDto input);
        Task<List<string>> AnswerQuestion2(TestPaperDto input);
        Task<List<AnswerDto>> AnswerQuestionAsync(string input, TestPaper entity);
        Task<string> CreateUser(UserInput input);
        Task<string> DescribeFileAsync(string md5);
        Task<ApplyFileUploadLease_BodyDto> GetApplyFileUploadLease_Body(FileInfoInput input);
        Task<bool> IsFileExistsAsync(string md5);
        Task<string> Login(LoginInput input);
        Task<List<OneClickOneToManyDto>> OneClickOneToMany(TestPaperDto input);
        Task<OneClickOneToOneDto> OneClickOneToOne(TestPaperDto input);
        Task<string> ResetPassword(ResetPasswordInput input);
        void SendEmailCode(string email);
        Task<string> SimplifyHtmlAsync(string htmlContent);
        Task<string> UploadFileAsync(IFormFile file);
        Task<string> UploadFile_AliAsync(IFormFile file);
    }
}
