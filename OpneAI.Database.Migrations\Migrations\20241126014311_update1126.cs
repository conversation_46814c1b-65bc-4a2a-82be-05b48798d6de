﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OpneAI.Database.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class update1126 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OnePaperOneTopic");

            migrationBuilder.RenameColumn(
                name: "LLM_Id",
                table: "DocFile",
                newName: "Model_name");

            migrationBuilder.AddColumn<string>(
                name: "File_id",
                table: "DocFile",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "UseCount",
                table: "ApiKey",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OrderId",
                table: "ApiKey",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TestPaperId",
                table: "ApiKey",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "UseTokens",
                table: "ApiKey",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "Order",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    OrderNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PaymentType = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<int>(type: "int", nullable: false),
                    IsPaid = table.Column<bool>(type: "bit", nullable: false),
                    PaidTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UserId = table.Column<int>(type: "int", nullable: true),
                    ProductInfo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TransactionID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Order", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Order_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_ApiKey_TestPaperId",
                table: "ApiKey",
                column: "TestPaperId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Order_UserId",
                table: "Order",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_ApiKey_TestPaper_TestPaperId",
                table: "ApiKey",
                column: "TestPaperId",
                principalTable: "TestPaper",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ApiKey_TestPaper_TestPaperId",
                table: "ApiKey");

            migrationBuilder.DropTable(
                name: "Order");

            migrationBuilder.DropIndex(
                name: "IX_ApiKey_TestPaperId",
                table: "ApiKey");

            migrationBuilder.DropColumn(
                name: "File_id",
                table: "DocFile");

            migrationBuilder.DropColumn(
                name: "OrderId",
                table: "ApiKey");

            migrationBuilder.DropColumn(
                name: "TestPaperId",
                table: "ApiKey");

            migrationBuilder.DropColumn(
                name: "UseTokens",
                table: "ApiKey");

            migrationBuilder.RenameColumn(
                name: "Model_name",
                table: "DocFile",
                newName: "LLM_Id");

            migrationBuilder.AlterColumn<int>(
                name: "UseCount",
                table: "ApiKey",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.CreateTable(
                name: "OnePaperOneTopic",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OnePaperOneTopic", x => x.Id);
                });
        }
    }
}
