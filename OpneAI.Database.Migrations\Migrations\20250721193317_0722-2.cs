﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OpneAI.Database.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class _07222 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Content",
                table: "TestPaper");

            migrationBuilder.DropColumn(
                name: "HtmlContent",
                table: "TestPaper");

            migrationBuilder.CreateTable(
                name: "TestPaper_html",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Number = table.Column<int>(type: "int", nullable: false),
                    Question = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Type = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    HtmlContent = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TestPaperId = table.Column<int>(type: "int", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TestPaper_html", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TestPaper_html_TestPaper_TestPaperId",
                        column: x => x.TestPaperId,
                        principalTable: "TestPaper",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_TestPaper_html_TestPaperId",
                table: "TestPaper_html",
                column: "TestPaperId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TestPaper_html");

            migrationBuilder.AddColumn<string>(
                name: "Content",
                table: "TestPaper",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HtmlContent",
                table: "TestPaper",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
