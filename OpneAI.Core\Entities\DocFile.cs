﻿using Furion.DatabaseAccessor;

namespace OpneAI.Core.Entities
{
    public class DocFile : Entity
    {
        public string Name { get; set; }
        public string MD5 { get; set; }
        public string Knowledge_id { get; set; }

        public string CategoryId { get; set; }

        public string WorkspaceId { get; set;} = "llm-1j6db34cwlrp3ju4";
        public bool IsDeleted { get; set; } = false;

        public string Gemini_Url { get; set; }
        public string R2_Url { get; set; }
        public string File_id { get; set; }
        public string Model_name { get; set; }

        public string SizeInBytes { get; set; }
    }
}
