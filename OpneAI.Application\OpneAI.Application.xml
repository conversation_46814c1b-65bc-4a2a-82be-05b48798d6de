<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OpneAI.Application</name>
    </assembly>
    <members>
        <member name="T:OpneAI.Application.SystemAppService">
            <summary>
            系统服务接口
            </summary>
        </member>
        <member name="M:OpneAI.Application.SystemAppService.AnswerQuestion(OpneAI.Application.System.Dtos.TestPaperDto)">
            <summary>
            导出题目
            </summary>
            <returns></returns>
        </member>
        <member name="M:HtmlSimplifier.SimplifyHtmlAsync(System.String)">
            <summary>
            将传入的HTML内容简化，以优化给大型语言模型（LLM）处理。
            该方法旨在提取核心结构和交互元素，同时最大限度地减少token消耗。
            </summary>
            <param name="htmlContent">原始HTML字符串。</param>
            <returns>一个简化的、对AI友好的HTML字符串。</returns>
        </member>
        <member name="M:HtmlSimplifier.RemoveUnnecessaryElements(AngleSharp.Dom.IDocument)">
            <summary>
            移除对LLM无用的大块元素。
            </summary>
        </member>
        <member name="M:HtmlSimplifier.RemoveComments(AngleSharp.Dom.INode)">
            <summary>
            递归移除所有注释节点。
            </summary>
        </member>
        <member name="M:HtmlSimplifier.IsElementHidden(AngleSharp.Dom.IElement)">
            <summary>
            检查元素是否对用户不可见。
            </summary>
        </member>
        <member name="M:HtmlSimplifier.StripUnwantedAttributes(AngleSharp.Dom.IElement)">
            <summary>
            从元素中移除不在白名单里的属性。
            </summary>
        </member>
        <member name="M:HtmlSimplifier.TransformSpecialElements(AngleSharp.Dom.IElement,AngleSharp.Dom.IDocument)">
            <summary>
            转换特定元素以优化表示。
            </summary>
        </member>
        <member name="M:HtmlSimplifier.CleanUpWhitespace(System.String)">
            <summary>
            清理最终HTML字符串中的多余空白。
            </summary>
        </member>
    </members>
</doc>
