using OpneAI.Application.System.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OpneAI.Application.Gemini.Services
{
    public interface IGeminiService
    {
        Task<List<QuestionsDto>> ExportQuestionsByGeminiAsync(string input);
        Task<QuestionsDto> ExportSingleQuestionByGemini(string input);
        Task<QuestionsDto> ExportSingleQuestionByGeminiJsonSchema(string input);
        Task<QuestionsDto> ExportSingleQuestionByGeminiReasoning(string input);
        Task<QuestionsDto> ExportSingleQuestionByGeminiReasoningJsonSchema(string input);
        Task<string> GetAnswerByGeminiAsync(string input);
        Task<List<OneClickOneToManyDto>> GetSelectorsByGeminiAsync(string input);
        Task<OneClickOneToOneDto> GetSingleAnswerByGeminiJsonSchemaAsync(string input);
    }
}