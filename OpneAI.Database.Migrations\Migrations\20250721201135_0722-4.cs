﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OpneAI.Database.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class _07224 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Type",
                table: "TestPaper_html",
                newName: "QuestionType");

            migrationBuilder.AddColumn<string>(
                name: "PaperType",
                table: "TestPaper_html",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PaperType",
                table: "TestPaper_html");

            migrationBuilder.RenameColumn(
                name: "QuestionType",
                table: "TestPaper_html",
                newName: "Type");
        }
    }
}
