using Newtonsoft.Json;
using OpneAI.Application.System.Dtos;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System;

namespace OpneAI.Application.Chatgpt.Services
{
    public class ChatgptService : IChatgptService, ITransient
    {
        private readonly string model_gpt_Apikey = "sk-ifvJs2QbtOO5h8VoC6Ae0f08F91c44E4Aa086e1eBd47E9A3";
        private readonly string model_gpt_Url = "https://api.gptnb.ai/v1/chat/completions";
        
        public async Task<List<QuestionsDto>> ExportQuestionsByGpt4ominiAsync(string input)
        {
            // 创建HttpClient实例
            var client = new HttpClient();
            // 设置请求头
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {model_gpt_Apikey}");
            client.Timeout = TimeSpan.FromMinutes(10);

            // 构造请求体
            var requestData = new
            {
                model = "gpt-4o-mini",
                //max_tokens = 4095,
                //temperature = 0.95,
                //top_p = 0.7,
                //do_sample = false,

                messages = new List<dynamic>
                {
                    new
                    {
                        role = "system",
                        content =
                            "你只负责提取给出文本中题目，输出为满足C#实体类Question的json格式，其中选项需要包含字母和具体的内容，输出格式为：A.xxx,B.xxx,C.xxx,D.xxx。选项之间用英文逗号隔开。题目类型为单选题、多选题、判断题、简答题。题目内容中不要包含任何特殊符号。直接输出为json格式，不要添加任何其他的东西。保持数据的完整性是最重要的，不要遗漏任何一题。publicclassQuestion{publicint编号{get;set;}publicstring题型{get;set;}publicstring试题{get;set;}publicList<string>?选项{get;set;}}"
                    },
                    new
                    {
                        role = "user",
                        content = input
                    }
                },
                stream = false
            };

            // 将请求体序列化为JSON字符串
            string jsonRequestData = JsonConvert.SerializeObject(requestData);

            // 创建HttpContent
            HttpContent content = new StringContent(jsonRequestData, Encoding.UTF8, "application/json");

            // 发送POST请求
            HttpResponseMessage response = await client.PostAsync(model_gpt_Url, content);

            // 读取响应内容
            string jsonResponseData = await response.Content.ReadAsStringAsync();
            var openAIOutput = JsonConvert.DeserializeObject<OpenAIOutput>(jsonResponseData);

            var message = openAIOutput.Choices.First().Message.Content;
            // 多轮对话处理
            while (openAIOutput.Choices.First().Finish_Reason == "length")
            {
                // 获取上一次对话的输出内容，作为新一轮对话的输入
                var previousMessage = openAIOutput.Choices.First().Message.Content;

                // 更新请求体中的messages
                requestData.messages.Add(new
                {
                    role = "assistant",
                    content = previousMessage
                });
                requestData.messages.Add(new
                {
                    role = "user",
                    content = "继续" // 这里应该是用户的新输入，这里暂时用"继续"代替
                });


                // 重新序列化请求体为JSON字符串
                jsonRequestData = JsonConvert.SerializeObject(requestData);

                // 更新HttpContent
                content = new StringContent(jsonRequestData, Encoding.UTF8, "application/json");

                // 发送新的POST请求
                response = await client.PostAsync(model_gpt_Url, content);

                // 读取新的响应内容
                jsonResponseData = await response.Content.ReadAsStringAsync();
                openAIOutput = JsonConvert.DeserializeObject<OpenAIOutput>(jsonResponseData);
                message = message + openAIOutput.Choices.First().Message.Content;
            }

            //var trimMessage1 = message.Substring(0, message.Length - 3);
            //var trimMessages2 = trimMessage1.Substring(8);
            //  var trimMessages2 = trimMessage1.Substring(8);
            var strTemp = RemovePatterns(message);
            if (!strTemp.TrimStart().StartsWith("["))
            {
                // 如果不是数组，则包装成单元素数组
                strTemp = $"[{strTemp}]";
            }

            var output = JsonConvert.DeserializeObject<List<QuestionsDto>>(strTemp);
            return output;
        }

        public async Task<List<QuestionOutput2>> ExportTitleByGpt4ominiAsync(string input)
        {
            var baseUrl = "https://api.gptnb.ai/v1/chat/completions";
            var apikey = "sk-ifvJs2QbtOO5h8VoC6Ae0f08F91c44E4Aa086e1eBd47E9A3";
            var client = new HttpClient();
            // 设置请求头
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Add("Content-Type", "application/json");
            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apikey}");
            client.Timeout = TimeSpan.FromMinutes(10);

            // 构造请求体
            var requestData = new
            {
                model = "gpt-4o-mini",
                // max_tokens = 4095,
                // temperature = 0.95,
                // top_p = 0.7,
                // do_sample = false,

                messages = new List<dynamic>
                {
                    new
                    {
                        role = "system",
                        content =
                            "你只负责提取给出文本中题目，输出为满足C#实体类Question的json格式，其中选项需要包含具体的内容,输出格式为'A.xxx;B.xxx'。直接输出为json格式，不要添加任何其他的东西。保持数据的完整性是最重要的，不要遗漏任何一题。publicclassQuestion{publicint编号{get;set;}publicstring题型{get;set;}publicstring试题{get;set;}publicstring选项{get;set;}}"
                    },
                    new
                    {
                        role = "user",
                        content = input
                    }
                },
                Stream = false
            };

            // 将请求体序列化为JSON字符串
            string jsonRequestData = JsonConvert.SerializeObject(requestData);

            // 创建HttpContent
            HttpContent content = new StringContent(jsonRequestData, Encoding.UTF8, "application/json");

            // 发送POST请求
            HttpResponseMessage response = await client.PostAsync(baseUrl, content);

            // 读取响应内容
            string jsonResponseData = await response.Content.ReadAsStringAsync();
            var openAIOutput = JsonConvert.DeserializeObject<OpenAIOutput>(jsonResponseData);

            var message = openAIOutput.Choices.First().Message.Content;
            // 多轮对话处理
            while (openAIOutput.Choices.First().Finish_Reason == "length")
            {
                // 获取上一次对话的输出内容，作为新一轮对话的输入
                var previousMessage = openAIOutput.Choices.First().Message.Content;

                // 更新请求体中的messages
                requestData.messages.Add(new
                {
                    role = "assistant",
                    content = previousMessage
                });
                requestData.messages.Add(new
                {
                    role = "user",
                    content = "继续" // 这里应该是用户的新输入，这里暂时用"继续"代替
                });


                // 重新序列化请求体为JSON字符串
                jsonRequestData = JsonConvert.SerializeObject(requestData);

                // 更新HttpContent
                content = new StringContent(jsonRequestData, Encoding.UTF8, "application/json");

                // 发送新的POST请求
                response = await client.PostAsync(model_gpt_Url, content);

                // 读取新的响应内容
                jsonResponseData = await response.Content.ReadAsStringAsync();
                openAIOutput = JsonConvert.DeserializeObject<OpenAIOutput>(jsonResponseData);
                message = message + openAIOutput.Choices.First().Message.Content;
            }

            // var trimMessage1 = message.Substring(0, message.Length - 3);
            // var trimMessages2 = trimMessage1.Substring(8);
            //  var trimMessages2 = trimMessage1.Substring(8);
            var outTemp = RemovePatterns(message);
            var output = JsonConvert.DeserializeObject<List<QuestionOutput2>>(outTemp);
            return output;
        }
        
        public static string RemovePatterns(string text)
        {
            // 定义要删除的模式
            string[] patterns = { "```json", "```" };

            // 对每个模式执行替换操作
            foreach (var pattern in patterns)
            {
                text = Regex.Replace(text, Regex.Escape(pattern), "");
            }

            return text;
        }
    }
}