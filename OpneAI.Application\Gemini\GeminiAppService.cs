using Microsoft.AspNetCore.Mvc;
using OpneAI.Application.Gemini.Services;
using OpneAI.Application.System.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OpneAI.Application.Gemini
{
    public class GeminiAppService : IDynamicApiController
    {
        private readonly IGeminiService _geminiService;
        public GeminiAppService(IGeminiService geminiService)
        {
            _geminiService = geminiService;
        }

        public Task<List<QuestionsDto>> ExportQuestionsByGeminiAsync([FromBody] string input)
        {
            return _geminiService.ExportQuestionsByGeminiAsync(input);
        }

        public Task<QuestionsDto> ExportSingleQuestionByGemini([FromBody] string input)
        {
            return _geminiService.ExportSingleQuestionByGemini(input);
        }
        public Task<QuestionsDto> ExportSingleQuestionByGeminiJsonSchema([FromBody] string input)
        {
            return _geminiService.ExportSingleQuestionByGeminiJsonSchema(input);
        }

        public Task<QuestionsDto> ExportSingleQuestionByGeminiReasoning([FromBody] string input)
        {
            return _geminiService.ExportSingleQuestionByGeminiReasoning(input);
        }
    }
}