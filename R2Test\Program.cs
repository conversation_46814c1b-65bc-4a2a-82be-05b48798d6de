using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using System;
using System.IO;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Cloudflare R2 存储测试程序 ===");

        // R2 配置
        var accessKeyId = "bf986ad420c993b42716d407f851c5e5";
        var secretAccessKey = "****************************************************************";
        var bucketName = "shengjibao";
        var accountId = "qsfrXbd_4FJ4XYJx59R8iXNBV9xXFDv7IsA6p2Dl";
        // 尝试不同的端点格式
        var serviceUrl1 = "https://e6b0d9806e7184b3a54e6c02919644cf.r2.cloudflarestorage.com";
        var serviceUrl2 = $"https://{accountId}.r2.cloudflarestorage.com";

        var serviceUrl = serviceUrl1; // 先使用原始配置

        Console.WriteLine($"配置信息:");
        Console.WriteLine($"  Account ID: {accountId}");
        Console.WriteLine($"  Bucket: {bucketName}");
        Console.WriteLine($"  Service URL: {serviceUrl}");
        Console.WriteLine();

        // 测试文件路径
        string testFilePath = @"C:\Users\<USER>\Downloads\deepwiki.xlsx";

        Console.WriteLine($"检查测试文件: {testFilePath}");
        if (!File.Exists(testFilePath))
        {
            Console.WriteLine($"❌ 测试文件不存在: {testFilePath}");
            Console.WriteLine("请确保文件路径正确");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
            return;
        }
        Console.WriteLine("✅ 测试文件存在");

        try
        {
            Console.WriteLine("开始测试 Cloudflare R2 存储...");
            
            // 配置 S3 客户端用于 Cloudflare R2
            var s3Config = new AmazonS3Config
            {
                ServiceURL = serviceUrl,
                ForcePathStyle = true,
                UseHttp = false, // 使用 HTTPS
                // 添加更多配置以解决 SSL 问题
                MaxErrorRetry = 3,
                Timeout = TimeSpan.FromMinutes(5)
            };

            Console.WriteLine($"S3 配置:");

            using var s3Client = new AmazonS3Client(accessKeyId, secretAccessKey, s3Config);
            
            // 测试连接 - 先尝试列出存储桶中的对象
            Console.WriteLine("测试连接到 R2...");
            try
            {
                // 直接尝试列出存储桶中的对象，而不是列出所有存储桶
                var listRequest = new ListObjectsV2Request
                {
                    BucketName = bucketName,
                    MaxKeys = 1
                };

                var listResponse = await s3Client.ListObjectsV2Async(listRequest);
                Console.WriteLine($"✅ 连接成功! 存储桶 '{bucketName}' 中有 {listResponse.KeyCount} 个对象");
            }
            catch (AmazonS3Exception ex)
            {
                Console.WriteLine($"❌ S3 错误: {ex.ErrorCode} - {ex.Message}");
                Console.WriteLine($"状态码: {ex.StatusCode}");
                return;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 连接失败: {ex.Message}");
                Console.WriteLine($"异常类型: {ex.GetType().Name}");
                return;
            }

            // 上传文件
            var fileInfo = new FileInfo(testFilePath);
            var key = $"test_{DateTime.Now:yyyyMMddHHmmss}_{fileInfo.Name}";
            
            Console.WriteLine($"正在上传文件: {fileInfo.Name}");
            Console.WriteLine($"文件大小: {fileInfo.Length / 1024.0 / 1024.0:F2} MB");
            Console.WriteLine($"存储键: {key}");

            using var fileStream = File.OpenRead(testFilePath);
            var putRequest = new PutObjectRequest
            {
                BucketName = bucketName,
                Key = key,
                InputStream = fileStream,
                ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            };

            var putResponse = await s3Client.PutObjectAsync(putRequest);
            
            if (putResponse.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                var fileUrl = $"https://pub-{bucketName}.r2.dev/{key}";
                Console.WriteLine("✅ 文件上传成功!");
                Console.WriteLine($"文件 URL: {fileUrl}");
                Console.WriteLine($"ETag: {putResponse.ETag}");
            }
            else
            {
                Console.WriteLine($"❌ 上传失败，状态码: {putResponse.HttpStatusCode}");
                return;
            }

            // 验证文件是否存在
            Console.WriteLine("验证文件是否存在...");
            try
            {
                var headRequest = new GetObjectMetadataRequest
                {
                    BucketName = bucketName,
                    Key = key
                };

                var headResponse = await s3Client.GetObjectMetadataAsync(headRequest);
                Console.WriteLine("✅ 文件存在验证成功!");
                Console.WriteLine($"文件大小: {headResponse.ContentLength} 字节");
                Console.WriteLine($"最后修改时间: {headResponse.LastModified}");
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                Console.WriteLine("❌ 文件不存在");
            }

            // 可选：删除测试文件
            Console.WriteLine("是否删除测试文件? (y/n)");
            var deleteChoice = Console.ReadLine();
            if (deleteChoice?.ToLower() == "y")
            {
                var deleteRequest = new DeleteObjectRequest
                {
                    BucketName = bucketName,
                    Key = key
                };

                var deleteResponse = await s3Client.DeleteObjectAsync(deleteRequest);
                if (deleteResponse.HttpStatusCode == System.Net.HttpStatusCode.NoContent)
                {
                    Console.WriteLine("✅ 测试文件已删除");
                }
                else
                {
                    Console.WriteLine($"❌ 删除失败，状态码: {deleteResponse.HttpStatusCode}");
                }
            }

        }
        catch (AmazonS3Exception ex)
        {
            Console.WriteLine($"❌ AWS S3 错误: {ex.ErrorCode} - {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 未知错误: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }
        
        Console.WriteLine("\n测试完成，按任意键退出...");
        Console.ReadKey();
    }
}
