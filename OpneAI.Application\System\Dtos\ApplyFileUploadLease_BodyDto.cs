﻿namespace OpneAI.Application.System.Dtos
{
    public class ApplyFileUploadLease_BodyDto
    {
        public int Status { get; set; }
        public string Message { get; set; }
        public string RequestId { get; set; }
        public Data Data { get; set; }
        public string Code { get; set; }
        public bool Success { get; set; }
    }

    public class Data
    {
        public string FileUploadLeaseId { get; set; }
        public string Type { get; set; }
        public Param Param { get; set; }
    }

    public class Param
    {
        public Dictionary<string, string> Headers { get; set; }
        public string Method { get; set; }
        public string Url { get; set; }
    }
}