﻿using Furion.DatabaseAccessor;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OpneAI.Core.Entities
{
    public class ApplyFileUploadLease_Body : Entity
    {
        public int Status { get; set; }
        public string Message { get; set; }
        public string RequestId { get; set; }
        public Data Data { get; set; }
        public string Code { get; set; }
        public bool Success { get; set; }
    }

    public class Data
    {
        [Required]
        public string FileUploadLeaseId { get; set; }
        public string Type { get; set; }
        public Param Param { get; set; }
    }

    public class Param
    {
        [NotMapped]
        public Dictionary<string, string> Headers { get; set; }
        public string Method { get; set; }
        public string Url { get; set; }

        [Column(TypeName = "nvarchar(max)")]
        public string HeadersJson
        {
            get => Headers != null ? JsonConvert.SerializeObject(Headers) : null;
            set => Headers = value != null ? JsonConvert.DeserializeObject<Dictionary<string, string>>(value) : null;
        }
    } 
}
