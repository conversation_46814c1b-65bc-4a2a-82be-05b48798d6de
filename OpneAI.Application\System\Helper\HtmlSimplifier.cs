﻿using AngleSharp;
using AngleSharp.Dom;
using AngleSharp.Html.Parser;
using System.Text.RegularExpressions;

public class HtmlSimplifier
{
    // 定义一个属性白名单，这些属性对于理解结构、内容和交互至关重要
    private static readonly HashSet<string> AllowedAttributes = new HashSet<string>
    {
        // 核心标识符，用于自动化选择器
        "id", "name", 
        // 链接和表单
        "href", "action", "method", "for",
        // 表单元素状态和类型
        "type", "value", "placeholder", "selected", "checked", "disabled", "readonly",
        // ARIA (可访问性) 属性，为LLM提供关键上下文
        // role 定义了元素的用途，aria-label 提供了文本替代
        "role", "aria-label", "aria-labelledby", "aria-describedby",
        // 图片和标题的描述性文本
        "alt", "title",
        // Class 属性虽然可能冗余，但对于某些框架（如Bootstrap）的结构识别和选择器生成至关重要，因此保留
        "class","data"
    };

    /// <summary>
    /// 将传入的HTML内容简化，以优化给大型语言模型（LLM）处理。
    /// 该方法旨在提取核心结构和交互元素，同时最大限度地减少token消耗。
    /// </summary>
    /// <param name="htmlContent">原始HTML字符串。</param>
    /// <returns>一个简化的、对AI友好的HTML字符串。</returns>
    public async Task<string> SimplifyHtmlAsync(string htmlContent)
    {
        if (string.IsNullOrWhiteSpace(htmlContent))
        {
            return string.Empty;
        }

        // 1. 使用AngleSharp解析HTML
        var context = BrowsingContext.New(Configuration.Default);
        var parser = context.GetService<IHtmlParser>();
        var document = await parser.ParseDocumentAsync(htmlContent);

        // 2. 移除对LLM上下文理解和自动化无用的元素
        RemoveUnnecessaryElements(document);

        // 3. 遍历所有剩余元素，进行清理和转换
        var allElements = document.QuerySelectorAll("*").ToList(); // 使用ToList()创建副本，因为我们可能会在迭代中修改DOM
        foreach (var element in allElements)
        {
            // 如果元素在之前的步骤中已被移除，则跳过
            if (element.ParentElement == null && element != document.DocumentElement) continue;

            // 3.1. 移除不可见的元素（对用户交互无意义）
            if (IsElementHidden(element))
            {
                element.Remove();
                continue;
            }

            // 3.2. 移除不在白名单中的属性
            StripUnwantedAttributes(element);

            // 3.3. 特殊处理某些标签，以更好地保留语义并减少token
            TransformSpecialElements(element, document);
        }

        // 4. 移除HTML注释
        RemoveComments(document);

        // 5. 生成简化后的HTML并进行最终清理
        string simplifiedHtml = document.Body.InnerHtml; // 通常我们只关心body部分

        // 6. 优化空白字符，减少token使用
        simplifiedHtml = CleanUpWhitespace(simplifiedHtml);

        return simplifiedHtml;
    }

    /// <summary>
    /// 移除对LLM无用的大块元素。
    /// </summary>
    private void RemoveUnnecessaryElements(IDocument document)
    {
        // script 和 style 包含代码和样式，对结构理解无用且token消耗巨大
        // noscript, svg, iframe, source, track, map, area 等通常不是核心内容
        var selectorsToRemove = "script, style, link[rel='stylesheet'], noscript, svg, iframe, source, track, map, area, base, meta";
        foreach (var element in document.QuerySelectorAll(selectorsToRemove))
        {
            element.Remove();
        }
    }

    /// <summary>
    /// 递归移除所有注释节点。
    /// </summary>
    private void RemoveComments(INode node)
    {
        var children = node.ChildNodes.ToList();
        foreach (var child in children)
        {
            if (child.NodeType == NodeType.Comment)
            {
                child.Parent?.RemoveChild(child);
            }
            else if (child.HasChildNodes)
            {
                RemoveComments(child);
            }
        }
    }


    /// <summary>
    /// 检查元素是否对用户不可见。
    /// </summary>
    private bool IsElementHidden(IElement element)
    {
        // 检查 'hidden' 属性
        if (element.HasAttribute("hidden"))
        {
            return true;
        }

        // 检查 style="display: none;"
        var style = element.GetAttribute("style");
        if (style != null && style.Contains("display: none"))
        {
            return true;
        }

        // 检查 aria-hidden="true"
        if (element.GetAttribute("aria-hidden") == "true")
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 从元素中移除不在白名单里的属性。
    /// </summary>
    private void StripUnwantedAttributes(IElement element)
    {
        var attributesToRemove = element.Attributes
            .Where(attr => !AllowedAttributes.Contains(attr.Name.ToLowerInvariant()))
            .Select(attr => attr.Name)
            .ToList();

        foreach (var attrName in attributesToRemove)
        {
            element.RemoveAttribute(attrName);
        }
    }

    /// <summary>
    /// 转换特定元素以优化表示。
    /// </summary>
    private void TransformSpecialElements(IElement element, IDocument document)
    {
        // 将图片转换为更简洁的文本表示，保留其alt文本
        if (element.TagName.Equals("IMG", StringComparison.OrdinalIgnoreCase))
        {
            var altText = element.GetAttribute("alt")?.Trim();
            var replacementText = !string.IsNullOrWhiteSpace(altText)
                ? $"[图片: {altText}]"
                : "[图片]";

            var textNode = document.CreateTextNode(replacementText);
            element.Parent?.ReplaceChild(textNode, element);
        }
        // 可以在这里添加更多转换规则，例如处理视频、音频等
    }

    /// <summary>
    /// 清理最终HTML字符串中的多余空白。
    /// </summary>
    private string CleanUpWhitespace(string html)
    {
        if (string.IsNullOrEmpty(html)) return string.Empty;

        // 移除标签之间的多余空白和换行符，但保留标签内的文本格式
        // 这个正则表达式会匹配 > 和 < 之间的空白字符（包括换行）
        html = Regex.Replace(html, @">\s+<", "><", RegexOptions.Singleline);
        // 移除多余的换行符和制表符，将多个空白压缩成一个
        html = Regex.Replace(html, @"\s{2,}", " ", RegexOptions.Singleline);
        // 移除开头和结尾的空白
        return html.Trim();
    }
}