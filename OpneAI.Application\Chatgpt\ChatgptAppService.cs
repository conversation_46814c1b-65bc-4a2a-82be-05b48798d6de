using Microsoft.AspNetCore.Mvc;
using OpneAI.Application.Chatgpt.Services;
using System.Collections.Generic;
using System.Threading.Tasks;
using OpneAI.Application.System.Dtos;

namespace OpneAI.Application.Chatgpt
{
    public class ChatgptAppService : IDynamicApiController
    {
        private readonly IChatgptService _chatgptService;

        public ChatgptAppService(IChatgptService chatgptService)
        {
            _chatgptService = chatgptService;
        }

        public async Task<List<QuestionsDto>> ExportQuestionsByGpt4ominiAsync([FromBody] string input)
        {
            return await _chatgptService.ExportQuestionsByGpt4ominiAsync(input);
        }

        public async Task<List<QuestionOutput2>> ExportTitleByGpt4ominiAsync([FromBody] string input)
        {
            return await _chatgptService.ExportTitleByGpt4ominiAsync(input);
        }
    }
}