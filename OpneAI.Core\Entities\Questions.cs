﻿using Furion.DatabaseAccessor;

namespace OpneAI.Core.Entities
{
    public class Questions : Entity
    {
        public int Number { get; set; }
        public string Type { get; set; }
        public string Question { get; set; }
        public string Option { get; set; }

        public string Answer { get; set; }

        public TestPaper TestPaper { get; set; }

        public string HtmlContent { get; set; }
    }
}
