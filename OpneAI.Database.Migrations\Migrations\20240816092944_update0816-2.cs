﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OpneAI.Database.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class update08162 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "BandUserId",
                table: "ApiKey",
                newName: "BanddingUserId");

            migrationBuilder.AddColumn<int>(
                name: "AnswerModel",
                table: "ApiKey",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "EndTime",
                table: "ApiKey",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ExportModel",
                table: "ApiKey",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TestType",
                table: "ApiKey",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AnswerModel",
                table: "ApiKey");

            migrationBuilder.DropColumn(
                name: "EndTime",
                table: "ApiKey");

            migrationBuilder.DropColumn(
                name: "ExportModel",
                table: "ApiKey");

            migrationBuilder.DropColumn(
                name: "TestType",
                table: "ApiKey");

            migrationBuilder.RenameColumn(
                name: "BanddingUserId",
                table: "ApiKey",
                newName: "BandUserId");
        }
    }
}
