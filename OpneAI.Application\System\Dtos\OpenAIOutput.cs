using System.Text.Json.Serialization;

namespace OpneAI.Application.System.Dtos
{
    public class OpenAIOutput
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("object")]
        public string ObjectType { get; set; }

        [JsonPropertyName("created")]
        public long Created { get; set; }

        [JsonPropertyName("model")]
        public string Model { get; set; }

        [JsonPropertyName("choices")]
        public Choice[] Choices { get; set; }

        [JsonPropertyName("usage")]
        public Usage Usage { get; set; }

        [JsonPropertyName("system_fingerprint")]
        public string System_Fingerprint { get; set; }
    }
    public class Choice
    {
        [JsonPropertyName("index")]
        public int Index { get; set; }

        [JsonPropertyName("message")]
        public Message Message { get; set; }

        [JsonPropertyName("logprobs")]
        public object? LogProbs { get; set; }

        [JsonPropertyName("finish_reason")]
        public string Finish_Reason { get; set; }
    }

    public class Message
    {
        [JsonPropertyName("role")]
        public string Role { get; set; }

        [JsonPropertyName("content")]
        public string Content { get; set; }

        [JsonPropertyName("refusal")]
        public string? Refusal { get; set; }
    }

    public class Usage
    {
        [JsonPropertyName("prompt_tokens")]
        public long Prompt_Tokens { get; set; }

        [JsonPropertyName("completion_tokens")]
        public long Completion_Tokens { get; set; }

        [JsonPropertyName("total_tokens")]
        public long Total_Tokens { get; set; }
    }
}