﻿using Furion.DatabaseAccessor;
using OpneAI.Core.Enums;
using System;

namespace OpneAI.Core.Entities
{
    public class Order : Entity
    {
        public string OrderNumber { get; set; }
        public PaymentType PaymentType { get; set; }
        public int Amount { get; set; }

        public bool IsPaid { get; set; }
        public DateTime PaidTime { get; set; }
        public User User { get; set; }
        public string ProductInfo { get; set; }
        public string TransactionID { get; set; }
    }
}
