﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OpneAI.Database.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class _0726 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Content",
                table: "DocFile",
                newName: "R2_Url");

            migrationBuilder.AddColumn<string>(
                name: "Gemini_Url",
                table: "DocFile",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Gemini_Url",
                table: "DocFile");

            migrationBuilder.RenameColumn(
                name: "R2_Url",
                table: "DocFile",
                newName: "Content");
        }
    }
}
