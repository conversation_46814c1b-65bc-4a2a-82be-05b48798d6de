﻿using Furion.DatabaseAccessor;

namespace OpneAI.Core.Entities
{
    public class TestPaper_html : Entity
    {
        public int? Number {  get; set; }
        public string Question {  get; set; }

        public string PaperType {  get; set; }
        public string QuestionType {  get; set; }
        public string Content { get; set; }

        public string HtmlContent { get; set; }

        public TestPaper TestPaper { get; set; }
    }
}
