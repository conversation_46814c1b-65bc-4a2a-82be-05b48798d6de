﻿using OpneAI.Application.System.Dtos;

namespace OpneAI.Application
{
    /// <summary>
    /// 系统服务接口
    /// </summary>
    public class SystemAppService : IDynamicApiController
    {
        private readonly ISystemService _systemService;
        public SystemAppService(ISystemService systemService)
        {
            _systemService = systemService;
        }

        /// <summary>
        /// 导出题目
        /// </summary>
        /// <returns></returns>




        //public Task<List<string>> AnswerQuestion([FromBody] string input)
        //{
        //    return _systemService.AnswerQuestionAsync(input);
        //}

        public Task<List<AnswerDto>> AnswerQuestion(TestPaperDto input)
        {
            return _systemService.AnswerQuestion(input);
        }

        public Task<List<string>> AnswerQuestion2(TestPaperDto input)
        {
            return _systemService.AnswerQuestion2(input);
        }

        public Task<string> RegisterUser(UserInput input)
        {
            return _systemService.CreateUser(input);
        }

        public Task<string> ResetPassword(ResetPasswordInput input)
        {
            return _systemService.ResetPassword(input);
        }

        public Task<string> Login(LoginInput input)
        {
            return _systemService.Login(input);
        }

        //发送邮件验证码
        public void SendEmailCode(string email)
        {
            _systemService.SendEmailCode(email);
        }

        public async Task<string> UploadFile_AliAsync(IFormFile file)
        {
            return await _systemService.UploadFile_AliAsync(file);
        }

        public async Task<string> UploadFileAsync(IFormFile file)
        {
            return await _systemService.UploadFileAsync(file);
        }

        //申请文档上传租约
        [HttpPost]
        public async Task<ApplyFileUploadLease_BodyDto> GetApplyFileUploadLease_Body(FileInfoInput input)
        {
            return await _systemService.GetApplyFileUploadLease_Body(input);
        }

        [HttpGet]
        public async Task<string> DescribeFileAsync(string md5)
        {
            return await _systemService.DescribeFileAsync(md5);
        }

        //查询文件是否存在
        [HttpGet]
        public async Task<bool> IsFileExistsAsync(string md5)
        {
            return await _systemService.IsFileExistsAsync(md5);
        }

        public async Task<List<OneClickOneToManyDto>> OneClickOneToMany(TestPaperDto input)
        {
            return await _systemService.OneClickOneToMany(input);
        }

        public async Task<OneClickOneToOneDto> OneClickOneToOne(TestPaperDto input)
        {
            return await _systemService.OneClickOneToOne(input);
        }

        public async Task<string> SimplifyHtmlAsync(string htmlContent)
        {
            return await _systemService.SimplifyHtmlAsync(htmlContent);
        }
    }
}
