﻿using Furion;
using System.Reflection;

namespace OpneAI.Web.Entry
{
    public class SingleFilePublish : ISingleFilePublish
    {
        public Assembly[] IncludeAssemblies()
        {
            return Array.Empty<Assembly>();
        }

        public string[] IncludeAssemblyNames()
        {
            return new[]
            {
                "OpneAI.Application",
                "OpneAI.Core",
                "OpneAI.EntityFramework.Core",
                "OpneAI.Web.Core"
            };
        }
    }
}