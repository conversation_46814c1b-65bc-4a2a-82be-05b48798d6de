using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using OpneAI.Application.System.Services;
using System.Security.Cryptography;
using System.Text;

namespace OpneAI.Application.System
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestFileUploadController : ControllerBase
    {
        private readonly IR2StorageService _r2StorageService;

        public TestFileUploadController(IR2StorageService r2StorageService)
        {
            _r2StorageService = r2StorageService;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("No file uploaded");
                }

                // 计算文件的 MD5 哈希
                var md5 = GetMD5Hash(file);
                
                // 上传到 R2
                var fileUrl = await _r2StorageService.UploadFileAsync(file, md5);

                return Ok(new
                {
                    success = true,
                    message = "File uploaded successfully",
                    data = new
                    {
                        fileName = file.FileName,
                        md5 = md5,
                        fileUrl = fileUrl,
                        fileSize = file.Length
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Upload failed",
                    error = ex.Message
                });
            }
        }

        [HttpGet("check/{key}")]
        public async Task<IActionResult> CheckFileExists(string key)
        {
            try
            {
                var exists = await _r2StorageService.FileExistsAsync(key);
                return Ok(new
                {
                    success = true,
                    exists = exists,
                    key = key
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Check failed",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("delete/{key}")]
        public async Task<IActionResult> DeleteFile(string key)
        {
            try
            {
                var deleted = await _r2StorageService.DeleteFileAsync(key);
                return Ok(new
                {
                    success = true,
                    deleted = deleted,
                    key = key
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Delete failed",
                    error = ex.Message
                });
            }
        }

        private string GetMD5Hash(IFormFile file)
        {
            using var md5 = MD5.Create();
            using var stream = file.OpenReadStream();
            var hash = md5.ComputeHash(stream);
            return Convert.ToHexString(hash).ToLowerInvariant();
        }
    }
}
