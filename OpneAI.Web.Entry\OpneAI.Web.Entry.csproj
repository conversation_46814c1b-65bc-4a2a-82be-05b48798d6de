﻿<Project Sdk="Microsoft.NET.Sdk.Web">




	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
		<PublishReadyToRunComposite>true</PublishReadyToRunComposite>
	</PropertyGroup>




	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="OpenAI" Version="2.2.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\OpneAI.Web.Core\OpneAI.Web.Core.csproj" />
	</ItemGroup>
	<ProjectExtensions>
		<VisualStudio>
			<UserProperties properties_4launchsettings_1json__JsonSchema="" />
		</VisualStudio>
	</ProjectExtensions>

</Project>
