﻿using Furion;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OpneAI.EntityFramework.Core;
using OpneAI.Application.Gemini.Services;
using OpneAI.Application.Chatgpt.Services;
using OpneAI.Application.Zhipu.Services;
using OpneAI.Application;
using OpneAI.Application.System.Services;

namespace OpneAI.Web.Core
{
    public class Startup : AppStartup
    {
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddConsoleFormatter();
            services.AddJwt<JwtHandler>();

            services.AddScoped<IGeminiService, GeminiService>();
            services.AddScoped<IChatgptService, ChatgptService>();
            services.AddScoped<IZhipuService, ZhipuService>();
            services.AddScoped<IR2StorageService, R2StorageService>();

            services.AddCorsAccessor();

            services.AddControllers()
                    .AddInjectWithUnifyResult();
            services.AddDatabaseAccessor(options =>
            {
                options.AddDbPool<DefaultDbContext>();
            });
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseCorsAccessor();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseInject(string.Empty);

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
