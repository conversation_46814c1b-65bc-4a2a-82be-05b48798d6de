﻿using Furion.DatabaseAccessor;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using OpneAI.Core.Entities;
using System.Collections.Generic;

namespace OpneAI.EntityFramework.Core
{
    [AppDbContext("SqlServerConnectionString", DbProvider.SqlServer)]
    public class DefaultDbContext : AppDbContext<DefaultDbContext>
    {
        public DefaultDbContext(DbContextOptions<DefaultDbContext> options) : base(options)
        {
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            //modelBuilder.Entity<TestPaper>()
            //    .HasOne(tp => tp.ApiKey)
            //    .WithOne(apiKey => apiKey.TestPaper)
            //    .HasForeignKey<ApiKey>(apiKey => apiKey.TestPaperId); // 指定外键属性
            //modelBuilder.Entity<Param>()
            //    .Property(e => e.Headers)
            //    .HasConversion(
            //v => JsonConvert.SerializeObject(v),
            //v => JsonConvert.DeserializeObject<Dictionary<string, string>>(v));
            modelBuilder.Entity<ApplyFileUploadLease_Body>()
            .OwnsOne(b => b.Data, data =>
            {
                data.WithOwner();
                data.OwnsOne(d => d.Param, param =>
                {
                    param.Property(p => p.HeadersJson).HasColumnName("Headers");
                });
            });
        }
    }
}
