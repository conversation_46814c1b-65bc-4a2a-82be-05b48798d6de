using OpneAI.Application.System.Dtos;
using OpneAI.Application.Zhipu.Services;

namespace OpneAI.Application.Zhipu
{
    public class ZhipuAppService : IDynamicApiController
    {
        private readonly IZhipuService _zhipuService;
        public ZhipuAppService(IZhipuService zhipuService)
        {
            _zhipuService = zhipuService;
        }

        public Task<QuestionsDto> ExportSingleQuestionByGlm([FromBody] string input)
        {
            return _zhipuService.ExportSingleQuestionByGLMReasoning(input);
        }
    }
}