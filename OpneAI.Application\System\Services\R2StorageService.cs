using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System;
using System.IO;
using System.Threading.Tasks;

namespace OpneAI.Application.System.Services
{
    public class R2StorageService : IR2StorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly IConfiguration _configuration;
        private readonly string _bucketName;

        public R2StorageService(IConfiguration configuration)
        {
            _configuration = configuration;
            var r2StorageConfig = _configuration.GetSection("R2Storage");
            var s3Config = new AmazonS3Config
            {
                ServiceURL = r2StorageConfig["ServiceURL"],
                ForcePathStyle = true,
                RegionEndpoint = RegionEndpoint.APSouth1
            };
            _s3Client = new AmazonS3Client(
                r2StorageConfig["AccessKeyId"],
                r2StorageConfig["SecretAccessKey"],
                s3Config
            );
            _bucketName = r2StorageConfig["BucketName"];
        }

        public async Task<string> UploadFileAsync(IFormFile file, string md5)
        {
            var fileExtension = Path.GetExtension(file.FileName);
            var key = $"{md5}{fileExtension}";

            using (var stream = file.OpenReadStream())
            {
                var request = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key,
                    InputStream = stream,
                    ContentType = file.ContentType,
                };
                await _s3Client.PutObjectAsync(request);
            }
            return $"{_s3Client.Config.ServiceURL}/{_bucketName}/{key}";
        }
    }
}