using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Net;
using System.Threading.Tasks;

namespace OpneAI.Application.System.Services
{
    public class R2StorageService : IR2StorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly IConfiguration _configuration;
        private readonly ILogger<R2StorageService> _logger;
        private readonly string _bucketName;
        private readonly string _serviceUrl;

        public R2StorageService(IConfiguration configuration, ILogger<R2StorageService> logger)
        {
            _configuration = configuration;
            _logger = logger;

            var r2StorageConfig = _configuration.GetSection("R2Storage");

            // 验证配置
            var accessKeyId = r2StorageConfig["AccessKeyId"];
            var secretAccessKey = r2StorageConfig["SecretAccessKey"];
            var bucketName = r2StorageConfig["BucketName"];
            var serviceUrl = r2StorageConfig["ServiceURL"];

            if (string.IsNullOrEmpty(accessKeyId) || string.IsNullOrEmpty(secretAccessKey) ||
                string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(serviceUrl))
            {
                throw new InvalidOperationException("R2Storage configuration is incomplete. Please check AccessKeyId, SecretAccessKey, BucketName, and ServiceURL.");
            }

            _bucketName = bucketName;
            _serviceUrl = serviceUrl.TrimEnd('/'); // 移除末尾的斜杠

            // 配置 S3 客户端用于 Cloudflare R2
            var s3Config = new AmazonS3Config
            {
                ServiceURL = serviceUrl,
                ForcePathStyle = true,
                UseHttp = false, // 使用 HTTPS
                MaxErrorRetry = 3, // 增加重试次数
                Timeout = TimeSpan.FromMinutes(10), // 增加超时时间
                // 不设置 RegionEndpoint，因为 Cloudflare R2 不需要
            };

            _s3Client = new AmazonS3Client(accessKeyId, secretAccessKey, s3Config);

            _logger.LogInformation("R2StorageService initialized successfully with bucket: {BucketName}", _bucketName);
        }

        public async Task<string> UploadFileAsync(IFormFile file, string md5)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    throw new ArgumentException("File is null or empty");
                }

                var fileExtension = Path.GetExtension(file.FileName);
                var key = $"{md5}{fileExtension}";

                _logger.LogInformation("Starting upload to R2: {FileName} with key: {Key}", file.FileName, key);

                // 添加重试逻辑
                int maxRetries = 3;
                for (int attempt = 1; attempt <= maxRetries; attempt++)
                {
                    try
                    {
                        using (var stream = file.OpenReadStream())
                        {
                            var request = new PutObjectRequest
                            {
                                BucketName = _bucketName,
                                Key = key,
                                InputStream = stream,
                                ContentType = file.ContentType ?? "application/octet-stream",
                                ServerSideEncryptionMethod = ServerSideEncryptionMethod.None
                            };

                            _logger.LogInformation("Upload attempt {Attempt}/{MaxRetries} for file: {FileName}", attempt, maxRetries, file.FileName);
                            var response = await _s3Client.PutObjectAsync(request);

                            if (response.HttpStatusCode == HttpStatusCode.OK)
                            {
                                var fileUrl = $"{_serviceUrl}/{_bucketName}/{key}";
                                _logger.LogInformation("File uploaded successfully to R2 on attempt {Attempt}: {FileUrl}", attempt, fileUrl);
                                return fileUrl;
                            }
                            else
                            {
                                throw new Exception($"Upload failed with status code: {response.HttpStatusCode}");
                            }
                        }
                    }
                    catch (Exception ex) when (attempt < maxRetries)
                    {
                        _logger.LogWarning(ex, "Upload attempt {Attempt} failed, retrying... Error: {ErrorMessage}", attempt, ex.Message);
                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt))); // 指数退避
                    }
                }

                // 如果所有重试都失败了，抛出异常
                throw new Exception($"Failed to upload file after {maxRetries} attempts");
            }
            catch (AmazonS3Exception ex)
            {
                _logger.LogError(ex, "AWS S3 error during upload: {ErrorCode} - {ErrorMessage}", ex.ErrorCode, ex.Message);
                throw new Exception($"R2 storage error: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during file upload to R2");
                throw new Exception($"Failed to upload file to R2 storage: {ex.Message}", ex);
            }
        }

        public async Task<bool> FileExistsAsync(string key)
        {
            try
            {
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = key
                };

                await _s3Client.GetObjectMetadataAsync(request);
                return true;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == HttpStatusCode.NotFound)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if file exists: {Key}", key);
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string key)
        {
            try
            {
                var request = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key
                };

                var response = await _s3Client.DeleteObjectAsync(request);
                return response.HttpStatusCode == HttpStatusCode.NoContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {Key}", key);
                throw;
            }
        }
    }
}