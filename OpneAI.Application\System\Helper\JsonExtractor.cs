﻿namespace OpneAI.Application.System.Helper
{
    public class JsonExtractor
    {
        public static string? ExtractJson(string inputText)
        {
            if (string.IsNullOrWhiteSpace(inputText))
            {
                return null;
            }

            const string startMarker = "```json";
            const string endMarker = "```";

            // 查找开始标记的位置
            int startIndex = inputText.IndexOf(startMarker);
            if (startIndex == -1)
            {
                // 没有找到开始标记
                return null;
            }

            // 计算JSON内容的实际起始位置（在开始标记之后）
            int contentStartIndex = startIndex + startMarker.Length;

            // 从JSON内容的起始位置开始，查找结束标记
            int endIndex = inputText.IndexOf(endMarker, contentStartIndex);
            if (endIndex == -1)
            {
                // 没有找到结束标记
                return null;
            }

            // 截取两个标记之间的内容
            string jsonContent = inputText.Substring(contentStartIndex, endIndex - contentStartIndex);

            // 使用 Trim() 移除可能存在的前后空白字符（如换行符）
            return jsonContent.Trim();
        }
    }
}
