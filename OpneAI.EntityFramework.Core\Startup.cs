﻿using Furion;
using Microsoft.Extensions.DependencyInjection;

namespace OpneAI.EntityFramework.Core
{
    public class Startup : AppStartup
    {
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddDatabaseAccessor(options =>
            {
                options.AddDbPool<DefaultDbContext>();
            }, "OpneAI.Database.Migrations");
        }
    }
}
