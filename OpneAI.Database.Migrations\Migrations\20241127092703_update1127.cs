﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OpneAI.Database.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class update1127 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ApplyFileUploadLease_Body",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Message = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RequestId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Data_FileUploadLeaseId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Data_Type = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Data_Param_Method = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Data_Param_Url = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Headers = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Success = table.Column<bool>(type: "bit", nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApplyFileUploadLease_Body", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ApplyFileUploadLease_Body");
        }
    }
}
