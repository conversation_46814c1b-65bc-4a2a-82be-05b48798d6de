{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  },
  "ConnectionStrings": {
    "SqlServerConnectionString": "Server=************,1433;Database=ExamHelper;User=sa;Password=*******;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=True;"
  },
  "JWTSettings": {
    "ValidateIssuerSigningKey": true, // 是否验证密钥，bool 类型，默认true
    "IssuerSigningKey": "9B55BC160F5BABADA4EEB30266612B63", // 密钥，string 类型，必须是复杂密钥，长度大于16，.NET8+ 长度需大于 32
    "ValidateIssuer": true, // 是否验证签发方，bool 类型，默认true
    "ValidIssuer": "升级宝", // 签发方，string 类型
    "ValidateAudience": true, // 是否验证签收方，bool 类型，默认true
    "ValidAudience": "shengjibao", // 签收方，string 类型
    "ValidateLifetime": true, // 是否验证过期时间，bool 类型，默认true，建议true
    "ExpiredTime": 120, // 过期时间，long 类型，单位分钟，默认20分钟
    "ClockSkew": 5, // 过期时间容错值，long 类型，单位秒，默认 5秒
    "Algorithm": "HS256" // 加密算法，string 类型，默认 HS256
  }
}