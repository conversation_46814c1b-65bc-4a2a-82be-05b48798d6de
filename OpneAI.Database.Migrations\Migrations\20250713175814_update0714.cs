﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OpneAI.Database.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class update0714 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "题型",
                table: "Questions",
                newName: "Type");

            migrationBuilder.RenameColumn(
                name: "选项",
                table: "Questions",
                newName: "Question");

            migrationBuilder.RenameColumn(
                name: "试题",
                table: "Questions",
                newName: "Option");

            migrationBuilder.RenameColumn(
                name: "编号",
                table: "Questions",
                newName: "Number");

            migrationBuilder.RenameColumn(
                name: "答案",
                table: "Questions",
                newName: "HtmlContent");

            migrationBuilder.RenameColumn(
                name: "Selector",
                table: "Questions",
                newName: "Answer");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Type",
                table: "Questions",
                newName: "题型");

            migrationBuilder.RenameColumn(
                name: "Question",
                table: "Questions",
                newName: "选项");

            migrationBuilder.RenameColumn(
                name: "Option",
                table: "Questions",
                newName: "试题");

            migrationBuilder.RenameColumn(
                name: "Number",
                table: "Questions",
                newName: "编号");

            migrationBuilder.RenameColumn(
                name: "HtmlContent",
                table: "Questions",
                newName: "答案");

            migrationBuilder.RenameColumn(
                name: "Answer",
                table: "Questions",
                newName: "Selector");
        }
    }
}
